<template>
  <div class="baseManagement systemDialogStyle">
    <!-- 统计数据卡片 -->
    <div class="baseManagement_dataList">
      <div class="dataList_item">
        <div class="dataList_item_text">
          <div>设备总数</div>
          <div>{{ areaStatus.devicesNumber }}</div>
        </div>
        <div class="dataList_item_img">
          <img src="../../../assets/image/managementSystem/icon7.png" alt="">
        </div>
      </div>
      <div class="dataList_item">
        <div class="dataList_item_text">
          <div>报警设备数</div>
          <div>{{ areaStatus.alarmDevicesNumber }}</div>
        </div>
        <div class="dataList_item_img">
          <img src="../../../assets/image/managementSystem/icon12.png" alt="">
        </div>
      </div>
      <div class="dataList_item">
        <div class="dataList_item_text">
          <div>离线设备数</div>
          <div>{{ areaStatus.offlineDevicesNumber }}</div>
        </div>
        <div class="dataList_item_img">
          <img src="../../../assets/image/managementSystem/icon9.png" alt="">
        </div>
      </div>
      <div class="dataList_item">
        <div class="dataList_item_text">
          <div>区域总数</div>
          <div>{{ areaStatus.areaNumber }}</div>
        </div>
        <div class="dataList_item_img">
          <img src="../../../assets/image/managementSystem/icon8.png" alt="">
        </div>
      </div>
      <!-- <div class="report_item" @click="reportHistoryDialog = true">
        <div class="report_item_img">
          <img src="../../../assets/image/managementSystem/icon13.png" alt="">
        </div>
        <div class="report_item_text">
          汇报记录
        </div>
      </div> -->
      <div class="report_item" @click="reportDialog = true">
        <div class="report_item_img">
          <img src="../../../assets/image/managementSystem/icon13.png" alt="">
        </div>
        <div class="report_item_text">
          概述信息管理
        </div>
      </div>
    </div>

    <div class="baseManagement_con">
      <div class="handleBox">
        <div class="handleBox_leftSection">
          <div class="handleBox_item systemFormStyle">
            <el-select v-model="selectedAreaId" clearable placeholder="请选择区域">
              <el-option
                v-for="item in areaList"
                :key="item.areaId"
                :label="item.areaName"
                :value="item.areaId">
              </el-option>
            </el-select>
          </div>
          <div class="handleBox_item systemSearchButtonStyle2" style="width: 76px;">
            <el-button @click="search">查询</el-button>
          </div>
        </div>
        <div class="handleBox_rightButtons">
          <div class="handleBox_item systemSearchButtonStyle2">
            <el-button @click="addDialogOpen">添加</el-button>
          </div>
          <div class="handleBox_item systemSearchButtonStyle2">
            <el-button @click="downloadList">下载</el-button>
          </div>
        </div>
      </div>
      <div class="tableBox tableBox1 systemTableStyle">
        <el-table
          :data="tableData"
          style="width: 100%"
          height="100%"
          @expand-change="handleExpandChange">
          <el-table-column type="expand">
            <template slot-scope="scope">
              <div v-if="scope.row.loading" style="text-align: center; padding: 20px;">
                <i class="el-icon-loading"></i> 加载中...
              </div>
              <el-table
                v-else
                :data="scope.row.equipmentData || []"
                style="width: 100%">
                <el-table-column
                  type="index" 
                  label="序号" 
                  align="center" 
                  width="90">
                </el-table-column>
                <el-table-column
                  align="center" 
                  label="设备名称">
                  <template slot-scope="scoped">
                    <div>
                      {{scoped.row.equipmentSimpleInformationVo.equipmentName}}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  align="center" 
                  label="设备状态"
                  width="180">
                  <template slot-scope="scoped">
                    <div>
                      {{scoped.row.state | stateFormat}}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  align="center" 
                  label="是否报警">
                  <template slot-scope="scoped">
                    <div v-if="scoped.row.alarmState">
                      是
                    </div>
                    <div v-else>
                      否
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  width="150"
                  label="操作">
                  <template slot-scope="scoped">
                    <span class="viewData" @click="openHistoryDialog(scoped.row)">
                      查看数据
                    </span>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-table-column>
          <el-table-column
            type="index" 
            label="序号" 
            align="center" 
            width="90">
          </el-table-column>
          <el-table-column
            align="center" 
            label="区域"
            width="300">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.areaVo.areaName}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="生长阶段"
            width="120">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.growStage ? (农作物生长期._lableOf(scoped.row.growStage) || '--') : '--'}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="作物"
            width="120">
            <template slot-scope="scoped">
              <div>
                {{scoped.row.crop ? (农作物种类._lableOf(scoped.row.crop) || '--') : '--'}}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="设备名称">
            <template slot-scope="scoped">
              <span v-for="(item,index) in scoped.row.equipmentSimpleInformationVo" :key="index">
                {{item.equipmentName}}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column
            align="center"
            width="150"
            label="操作">
            <template slot-scope="scoped">
              <span class="viewData" @click="editDialogOpen(scoped.row)">编辑</span>
              <span class="viewData deleteBtn" @click="deleteArea(scoped.row)">删除</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pageBox systemPageChange">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"  :page-size="pageSize" background layout="prev, pager, next " :total="total" :page-count="pageCount" :pager-count="9">
          </el-pagination>
      </div>
    </div>
    <!-- 汇报弹窗组件 -->
    <ReportDialog
      :visible.sync="reportDialog"
      @report-success="getList" />
    <!-- 汇报历史弹窗组件 -->
    <ReportHistoryDialog :visible.sync="reportHistoryDialog" />
    <!-- 编辑区域弹窗组件 -->
    <EditAreaDialog
      ref="editAreaDialog"
      :visible.sync="editDialog"
      @edit-success="getList" />
    
    <!-- 新增区域弹窗组件 -->
    <AddAreaDialog
      ref="addAreaDialog"
      :visible.sync="addDialog"
      @add-success="getList" />
    <BaseEquipmentHistoryDialog/>
  </div>
</template>
<script>

import 农作物种类 from '../../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物种类.js';
import 农作物生长期 from '../../../jaxrs/constants/com.zny.ia.constant.ProdConstant$农作物生长期.js';
import BaseEquipmentHistoryDialog from './components/baseEquipmentHistoryDialog.vue';
import CommonService from '../../../jaxrs/concrete/com.zny.ia.api.CommonService.js';
import BaseManagementService from '../../../jaxrs/concrete/com.zny.ia.api.ConcreteService.BaseManagementService.js';

// 导入子组件
import ReportDialog from './components/ReportDialog.vue';
import ReportHistoryDialog from './components/ReportHistoryDialog.vue';
import EditAreaDialog from './components/EditAreaDialog.vue';
import AddAreaDialog from './components/AddAreaDialog.vue';

export default {
  components: {
    BaseEquipmentHistoryDialog,
    ReportDialog,
    ReportHistoryDialog,
    EditAreaDialog,
    AddAreaDialog
  },
  data(){
    return{
      administrators:"admin",//管理员：admin->总,subAdmin->分管理员
      areaStatus:{
        devicesNumber: 0,        // 设备总数
        alarmDevicesNumber: 0,   // 报警设备数
        offlineDevicesNumber: 0, // 离线设备数
        areaNumber: 0            // 区域总数
      },//获取列表上方数据统计

      cropList: 农作物种类._toArray(), // 作物种类列表
      tableData: [],
      excelData:[],
      // 分页
      pageSize:20,
      currentPage:1,
      total:0,
      pageCount:1,
      // 弹窗状态
      reportDialog: false,
      reportHistoryDialog: false,
      editDialog: false,
      addDialog: false,
      // 新增：区域下拉选择
      areaList: [],
      selectedAreaId: null,
    }
  },

  mounted(){
    let role = localStorage.getItem('userRoles')
    if(role.indexOf(0) == -1){
      this.administrators='subAdmin'
    }else{
      this.administrators='admin'
    }
    this.getAllAreaListInformation()
    this.getList();
  },
  methods:{
    // 获取所有区域列表
    getAllAreaListInformation(){
      CommonService.allAreaOfCurrentUserManage()
      .then(res=>{
        // 用于区域下拉选择框
        this.areaList = res;
      })
    },
    // 获取列表上方数据统计
    getAreaStatisticsData(){
      // 始终加载基地总览数据
      BaseManagementService.areaStatistics()
      .then(res=>{
        console.log(res);
        this.areaStatus=res
      })
    },
    // 查询
    search(){
      this.currentPage=1
      this.getList()
    },
    // 获取列表
    getList(){
      // 根据选择的区域ID获取区域名称
      let areaName = null;
      if (this.selectedAreaId) {
        const selectedArea = this.areaList.find(area => area.areaId === this.selectedAreaId);
        areaName = selectedArea ? selectedArea.areaName : null;
      }

      // 始终加载基地总览数据
      BaseManagementService.baseAreaOverviewInformation(this.currentPage,this.pageSize,null,areaName)
      .then(res=>{
        this.tableData=res.list
        this.pageCount=res.count
        this.count=res.total

        // 初始化每行的加载状态和设备数据
        this.tableData.forEach(item => {
          this.$set(item, 'loading', false);
          this.$set(item, 'equipmentData', null);
          this.$set(item, 'expanded', false);
        });
      })

      // 获取统计数据
      this.getAreaStatisticsData();
    },
    // 处理表格展开/收起事件
    handleExpandChange(row, expandedRows) {
      const isExpanded = expandedRows.some(expandedRow => expandedRow === row);

      if (isExpanded && !row.equipmentData && !row.loading) {
        // 展开且未加载数据，开始加载
        this.$set(row, 'loading', true);
        this.$set(row, 'expanded', true);

        if (row.areaVo && row.areaVo.areaId) {
          BaseManagementService.areaOverviewInformation(1, 100, row.areaVo.areaId)
            .then(res => {
              this.$set(row, 'equipmentData', res.list);
              this.$set(row, 'loading', false);
            })
            .catch(error => {
              console.error('加载设备数据失败:', error);
              this.$set(row, 'loading', false);
              this.$message({
                message: '加载设备数据失败，请稍后重试',
                type: 'error'
              });
            });
        } else {
          this.$set(row, 'loading', false);
        }
      } else if (!isExpanded) {
        // 收起时更新状态
        this.$set(row, 'expanded', false);
      }
    },

    handleSizeChange(){
      this.currentPage=1
      this.getList()
    },
    handleCurrentChange(val){
      this.currentPage = val;
      this.getList()
    },
    // 打开历史记录弹窗
    openHistoryDialog(row){
      // 更新后的传参方式，直接使用行数据中的信息
      let obj={
        areaId: row.areaId || 0, // 如果没有areaId则默认使用0
        deviceType: row.equipmentSimpleInformationVo.deviceType
      }
      this.zEmit('openbaseEquipmentHistoryDialog',obj);
    },

    // 打开编辑弹窗
    editDialogOpen(row){
      this.editDialog = true;
      this.$refs.editAreaDialog.openDialog(row);
    },

    
    // 打开新增区域弹窗
    addDialogOpen(){
      this.addDialog = true;
      this.$refs.addAreaDialog.openDialog();
    },

    // 删除区域
    deleteArea(row){
      const areaId = row.areaVo.areaId;
      const areaName = row.areaVo.areaName;

      this.$confirm(`确定要删除区域"${areaName}"吗？删除后将无法恢复。`, '删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }).then(() => {
        // 调用删除接口
        BaseManagementService.deleteAreaByAreaId(areaId)
        .then(() => {
          this.$message({
            message: '删除成功',
            type: 'success'
          });
          // 刷新列表
          this.getList();
        })
        .catch(error => {
          console.error('删除失败:', error);
          this.$message({
            message: '删除失败，请稍后重试',
            type: 'error'
          });
        });
      }).catch(() => {
        // 用户取消删除
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    // 下载列表
    downloadList(){
      // 根据选择的区域ID获取区域名称
      let areaName = null;
      if (this.selectedAreaId) {
        const selectedArea = this.areaList.find(area => area.areaId === this.selectedAreaId);
        areaName = selectedArea ? selectedArea.areaName : null;
      }

      BaseManagementService.baseAreaOverviewInformation(0,0,null,areaName)
      .then(res=>{
        
        res.list.forEach(v=>{
          v.areaName=v.areaVo.areaName
          let equipmentName=[]
          v.equipmentSimpleInformationVo.forEach(e=>{
            equipmentName.push(e.equipmentName)
          })
          v.equipmentName=equipmentName.toString()
          // 处理作物名称
          v.cropName = v.crop ? (农作物种类._lableOf(v.crop) || '--') : '--'
          // 处理生长阶段名称
          v.growStageName = v.growStage ? (农作物生长期._lableOf(v.growStage) || '--') : '--'
        })
        this.excelData=res.list
        this.export2Excel()
      })
    },
    //表格数据写入excel
    export2Excel() {
      var that = this;
      require.ensure([], () => {
        const {
          export_json_to_excel_productPlan
        } = require("@/excel/Export2Excel.js");
        var tHeader=[]
        var filterVal = []
        tHeader = ["区域","设备名称","作物","生长阶段"]
        filterVal = [
          "areaName",
          "equipmentName",
          "cropName",
          "growStageName",
        ];
        const list = that.excelData;
        const data = that.formatJson(filterVal, list);
        export_json_to_excel_productPlan(tHeader, data, "基地总览数据表");
      });
    },
    //格式转换，直接复制即可,不需要修改什么
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => v[j]));
    },
  },
}
</script>
<style lang="less">
@import '../../../assets/css/system/baseManagement.less';
@media screen and (max-width: 1280px){
  .baseManagement{
    .editDialog, .addDialog{
      .el-dialog{
        width:48.5% !important;
      }
    }     
    .reportDialog{
      .el-dialog{
        width: 30% !important;
        .el-dialog__body{
          .formList{
            .systemFormStyle{
              width: 338px;
            }
          }
        }
      }
    } 
  } 
}
@media screen and (min-width: 1280px) and (max-width: 1680px){
  .baseManagement{
    .editDialog, .addDialog{
      .el-dialog{
        .el-dialog__body{
          .formList{
            .el-form{
              .el-input{
                width: 235px;
              }
              .formItem1{
                .el-form-item{
                  .el-form-item__content{
                    .systemFormStyle{
                      width: 235px;
                    }
                  }
                }
              }   
            }
          }
          
        }
      }
    }     
    .reportDialog{
      .el-dialog{
        .el-dialog__body{
          .formList{
            .systemFormStyle{
              width: 338px;
            }
          }
        }
      }
    } 
  } 
}

/* 展开行样式 */
.baseManagement {
  .el-table__expanded-cell {
    padding: 0 !important;

    .el-table {
      margin-bottom: 0;
      background-color: #f6f8fa;

      .el-table__header-wrapper {
        th {
          background-color: #eef1f6;
          color: #606266;
        }
      }

      .el-table__body-wrapper {
        .viewData {
          cursor: pointer;
          color: #409EFF;
        }
      }
    }
  }

  /* 删除按钮样式 */
  .deleteBtn {
    color: #F56C6C !important;

    &:hover {
      color: #F78989 !important;
    }
  }
}
</style>