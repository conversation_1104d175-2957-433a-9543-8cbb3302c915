<template>
    <div class="seedlingGrowthHistoryPhotoDialog dialogStyle">
        <el-dialog
            :title="title"
            width="1480px"
            center
            :visible.sync="dialogVisible"
            :modal-append-to-body="false"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="line"></div>
            <div class="close" @click="dialogVisible = false">
                <img src="../../assets/image/centralControlPlatform/close.png" alt="" />
            </div>
            <div class="handleBox">
                <div class="handleBox_select formStyle">
                    <el-select
                        v-model="equipmentId"
                        popper-class="selectStyle_list"
                        clearable
                        placeholder="请选择监控设备"
                    >
                        <el-option
                            v-for="item in equipmentData"
                            :key="item.equipmentId"
                            :label="item.equipmentName"
                            :value="item.equipmentId"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_select formStyle">
                    <el-select
                        v-model="fromId"
                        clearable
                        @change="fromDataChange"
                        popper-class="selectStyle_list"
                        placeholder="请选择数据来源"
                    >
                        <el-option
                            v-for="item in fromData"
                            :key="item.value"
                            :label="item.key"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </div>
                <div class="handleBox_item formStyle">
                    <el-date-picker
                        v-model="time"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        @change="timeChange"
                        popper-class="datePickerStyle"
                    ></el-date-picker>
                </div>
                <div class="handleBox_search searchButtonStyle2" @click="searchList()">
                    <el-button>查询</el-button>
                </div>
                <div class="handleBox_search searchButtonStyle2" @click="seedlingGrowthUploadPhotoDialogOpen()">
                    <el-button>上传</el-button>
                </div>
            </div>
            <div class="tableBox tableStyle">
                <el-table :data="tableData" border style="width: 100%" height="405px">
                    <el-table-column type="index" label="序号" align="center" width="80"></el-table-column>
                    <el-table-column prop="areaName" label="所属区域" align="center"></el-table-column>
                    <el-table-column prop="equipmentName" label="当前设备" align="center"></el-table-column>
                    <el-table-column label="拍摄图片" align="center">
                        <template slot-scope="scope">
                            <img :src="scope.row.picture" alt="" class="image" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="shootTime" label="拍摄时间" align="center"></el-table-column>
                    <!-- <el-table-column label="相关视频" align="center">
                        <template slot-scope="scope">
                            <div class="link_text" @click="openVideo(scope.row.videoAbout)">链接</div>
                        </template>
                    </el-table-column> -->
                </el-table>
            </div>
        </el-dialog>

        <!-- 上传图片弹窗 -->
        <seedlingGrowthUploadPhotoDialog />
    </div>
</template>
<script>
import CropCaseService from '../../jaxrs/concrete/com.zny.ia.api.CropCaseService'
import fromData from '../../jaxrs/constants/com.zny.ia.constant.ProdConstant$数据来源'
import CommonService from '../../jaxrs/concrete/com.zny.ia.api.CommonService'
import seedlingGrowthUploadPhotoDialog from './seedlingGrowthUploadPhotoDialog.vue'
export default {
    name: 'seedlingGrowthHistoryPhotoDialog',
    components:{
        seedlingGrowthUploadPhotoDialog,
    },
    data() {
        return {
            title: '历史图片',
            dialogVisible: false,
            equipmentData: [],
            fromId: null,
            fromData: fromData._toArray(),
            tableData: [],
            time: [],
            areaId: '',
            startTime: '',
            endTime: '',
            equipmentId: '',
        }
    },
    mounted() {
        this.listen('seedlingGrowthHistoryPhotoDialogOpen', areaId => {
            this.dialogVisible = true
            this.areaId = areaId
            // this.fromId=this.fromData[0].value
            this.getEquipmentData()
            this.forList()
        })
        //上传成功
        this.listen('seedlingGrowthUploadSuccess', () => {
            this.forList()
        })
    },
    methods: {
        //打开上传图片弹窗
        seedlingGrowthUploadPhotoDialogOpen() {
            this.zEmit('seedlingGrowthUploadPhotoDialogOpen')
        },
        //查询
        searchList() {
            this.forList()
        },
        //获取设备数据
        getEquipmentData() {
            CommonService.equipmentListByAreaId(this.areaId).then(res => {
                this.equipmentData = res
            })
        },
        //来源改变
        fromDataChange(val) {
            if (val === '') this.fromId = null
        },
        //获取列表
        forList() {
            var param = {
                num: 1,
                pageSize: 10,
                condition: {
                    areaId: this.areaId,
                    endTime: this.endTime,
                    equipmentId: this.equipmentId,
                    from: this.fromId,
                    startTime: this.startTime,
                },
            }
            CropCaseService.cropCaseHistory(param).then(res => {
                this.tableData = res.list
            })
        },
        //时间改变
        timeChange(val) {
            if (val == null || val == undefined || val == '') {
                this.startTime = ''
                this.endTime = ''
            } else {
                var arr = []
                for (var i = 0; i < val.length; i++) {
                    var y = val[i].getFullYear()
                    var m = val[i].getMonth() + 1
                    m = m < 10 ? '0' + m : m
                    var d = val[i].getDate()
                    d = d < 10 ? '0' + d : d
                    var time = y + '-' + m + '-' + d
                    arr.push(time)
                }
                this.startTime = arr[0]
                this.endTime = arr[1]
            }
        },
        //打开视频
        openVideo(val) {
            if (val) {
                window.open(val, '_blank')
            }
        },
    },
    beforeDestroy() {
        this.removeAllListener()
    },
}
</script>
<style lang="less">
@import '../../assets/css/Dialog/seedlingGrowthHistoryPhotoDialog.less';
</style>
